# Generated manually for shared product images feature

from django.db import migrations, models
import django.db.models.deletion
import cloudinary.models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0003_alter_product_title'),
    ]

    operations = [
        migrations.CreateModel(
            name='SharedProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', cloudinary.models.CloudinaryField(max_length=255, verbose_name='image')),
                ('alternative_text', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, editable=False)),
                ('updated_at', models.DateTimeField(auto_now=True, editable=False)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_images', to='products.product')),
            ],
            options={
                'verbose_name': 'Shared Product Image',
                'verbose_name_plural': 'Shared Product Images',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductVariantImageLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(help_text="Display order for this shared image within the variant's image gallery")),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, editable=False)),
                ('product_variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_image_links', to='products.productvariant')),
                ('shared_image', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variant_links', to='products.sharedproductimage')),
            ],
            options={
                'verbose_name': 'Product Variant Image Link',
                'verbose_name_plural': 'Product Variant Image Links',
                'ordering': ['product_variant', 'order'],
            },
        ),
        migrations.AddConstraint(
            model_name='productvariantimagelink',
            constraint=models.UniqueConstraint(fields=('product_variant', 'shared_image'), name='unique_variant_shared_image'),
        ),
        migrations.AddConstraint(
            model_name='productvariantimagelink',
            constraint=models.UniqueConstraint(fields=('product_variant', 'order', 'is_active'), name='unique_variant_order_active'),
        ),
    ]
