# Generated by Django 5.2.5 on 2025-09-29 03:42

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_remove_productvariantimagelink_unique_variant_shared_image_and_more'),
        ('staff', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductVariantImageLinkProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Variant Image Link',
                'verbose_name_plural': 'Staff Product Variant Image Links',
                'permissions': [('manage_image_links', 'Can manage product variant image links'), ('reorder_linked_images', 'Can reorder linked images')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.productvariantimagelink',),
        ),
        migrations.CreateModel(
            name='SharedProductImageProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Shared Product Image',
                'verbose_name_plural': 'Staff Shared Product Images',
                'permissions': [('manage_shared_images', 'Can manage shared product images'), ('convert_images', 'Can convert between direct and shared images')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.sharedproductimage',),
        ),
    ]
