import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { 
  SharedProductImageCreateData, 
  ProductVariantImageLinkCreateData,
  CombinedProductImage 
} from '../../types/api-types';

/**
 * Hook for fetching shared images for a product
 */
export const useSharedProductImages = (productId: number) => {
  return useQuery({
    queryKey: queryKeys.products.sharedImages(productId),
    queryFn: () => ProductService.getSharedProductImages(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching combined images (direct + shared) for a variant
 */
export const useCombinedProductImages = (variantId: number) => {
  return useQuery({
    queryKey: queryKeys.products.combinedImages(variantId),
    queryFn: () => ProductService.getCombinedProductImages(variantId),
    enabled: !!variantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook for uploading shared product image
 */
export const useUploadSharedProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.uploadSharedProductImage,
    onSuccess: (newImage) => {
      showSuccess('Shared Image Uploaded', 'Shared product image has been uploaded successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.sharedImages(newImage.product) });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Upload Failed', error.message || 'Failed to upload shared product image.');
    },
  });
};

/**
 * Hook for updating shared product image
 */
export const useUpdateSharedProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<SharedProductImageCreateData> }) =>
      ProductService.updateSharedProductImage(id, data),
    onSuccess: (updatedImage) => {
      showSuccess('Shared Image Updated', 'Shared product image has been updated successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.sharedImages(updatedImage.product) });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update shared product image.');
    },
  });
};

/**
 * Hook for deleting shared product image
 */
export const useDeleteSharedProductImage = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.deleteSharedProductImage,
    onSuccess: () => {
      showSuccess('Shared Image Deleted', 'Shared product image has been deleted successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete shared product image.');
    },
  });
};

/**
 * Hook for linking shared image to variant
 */
export const useLinkSharedImageToVariant = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.linkSharedImageToVariant,
    onSuccess: (link) => {
      showSuccess('Image Linked', 'Shared image has been linked to variant successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.combinedImages(link.product_variant) });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.variantImageLinks(link.product_variant) });
    },
    onError: (error: any) => {
      showError('Link Failed', error.message || 'Failed to link shared image to variant.');
    },
  });
};

/**
 * Hook for unlinking shared image from variant
 */
export const useUnlinkSharedImageFromVariant = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ProductService.unlinkSharedImageFromVariant,
    onSuccess: () => {
      showSuccess('Image Unlinked', 'Shared image has been unlinked from variant successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Unlink Failed', error.message || 'Failed to unlink shared image from variant.');
    },
  });
};

/**
 * Hook for converting direct image to shared image
 */
export const useConvertImageToShared = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ imageId, data }: { imageId: number; data: { link_to_variants?: number[] } }) =>
      ProductService.convertImageToShared(imageId, data),
    onSuccess: (result) => {
      showSuccess('Image Converted', 'Direct image has been converted to shared image successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Conversion Failed', error.message || 'Failed to convert image to shared.');
    },
  });
};

/**
 * Hook for converting shared image to direct image
 */
export const useConvertSharedImageToDirect = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ sharedImageId, variantId }: { sharedImageId: number; variantId: number }) =>
      ProductService.convertSharedImageToDirect(sharedImageId, variantId),
    onSuccess: (result) => {
      showSuccess('Image Converted', 'Shared image has been converted to direct image successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
    },
    onError: (error: any) => {
      showError('Conversion Failed', error.message || 'Failed to convert shared image to direct.');
    },
  });
};

/**
 * Hook for reordering variant image links
 */
export const useReorderVariantImageLinks = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({ variantId, orderedLinkIds }: { variantId: number; orderedLinkIds: number[] }) =>
      ProductService.reorderVariantImageLinks(variantId, orderedLinkIds),
    onSuccess: (result, variables) => {
      showSuccess('Images Reordered', 'Variant image links have been reordered successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.products.combinedImages(variables.variantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.products.variantImageLinks(variables.variantId) });
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder variant image links.');
    },
  });
};
