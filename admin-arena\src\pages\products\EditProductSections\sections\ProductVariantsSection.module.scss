@use '../../../../scss/variables.scss' as *;
@use '../../../../scss/mixins.scss' as *;

.header {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-md;

  div {
    h2 {
      @include heading-md;
      margin: 0 0 $spacing-xs 0;
      color: $text-primary;
    }

    p {
      @include text-sm;
      margin: 0;
      color: $text-secondary;
    }
  }
}

.emptyState {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;
  color: $text-secondary;

  p {
    @include text-base;
    margin: 0;
  }
}

.variantsList {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.variantCard {
  @include draggable-item;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: $spacing-lg;
  background-color: $background-secondary;
  display: flex;
  gap: $spacing-md;
  transition: border-color 0.2s;
  margin-bottom: $spacing-lg;

  &:hover {
    border-color: $primary-light;
    // No shadow or scale on hover
  }
}

.dragHandleContainer {
  position: absolute;
  top: 50%;
  right: $spacing-lg;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 2;
}

.dragHandle {
  // cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  background: $background-secondary;
  border-radius: 50%;
  padding: 0.25rem;
  transition: background 0.15s;
  box-shadow: none;

  &:hover {
    background: $background-hover;
  }
}

.orderBadge {
  @include text-xs;
  background-color: $background-secondary;
  color: $text-secondary;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-weight: 500;
}

.variantDisplay {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-lg;
}

.variantInfo {
  flex: 1;
  min-width: 0;
}

.variantHeader {
  @include flex-between;
  align-items: center;
  margin-bottom: $spacing-md;
  border-bottom: 1px solid $border-color;
  padding-bottom: $spacing-md;

  h4 {
    @include heading-lg;
    margin: 0;
    color: $text-primary;
    font-weight: 700;
    letter-spacing: 0.5px;
  }

  .variantActions {
    margin-left: auto;
    align-self: flex-start;
    position: relative;
    top: 0;
    right: 0;
    gap: $spacing-sm;
    display: flex;
  }
}

.variantBadges {
  @include flex-start;
  gap: $spacing-sm;
  margin-left: 1rem;
}

.variantDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem;
  margin-top: 0.5rem;
  align-items: center;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  min-width: 120px;
  margin-right: 1.5rem;

  .label {
    @include text-xs;
    font-weight: 500;
    color: $text-secondary;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .value {
    @include text-base;
    color: $text-primary;
    font-weight: 600;
    letter-spacing: 0.2px;
  }
}

// .variantActions is now in .variantHeader
.variantActions {
  button {
    border-radius: 6px;
    transition: background 0.15s;

    &:hover {
      background: $background-hover;
    }
  }
}

.deleteButton {
  color: $error-color;

  &:hover {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }
}

.editForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: $spacing-lg;
  align-items: start;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  label {
    @include text-sm;
    font-weight: 500;
    color: $text-primary;
  }
}

.switchGroup {
  @include flex-column;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm 0;
  text-align: center;
  height: 100%;

  label {
    margin: 0 0 $spacing-sm 0;
    font-weight: 500;
  }
}

.formActions {
  @include flex-end;
  gap: $spacing-md;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.modalForm {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.modalActions {
  @include flex-end;
  gap: $spacing-md;
  padding-top: $spacing-lg;
  border-top: 1px solid $border-color;
}

.deleteModal {
  text-align: center;

  p {
    @include text-base;
    color: $text-primary;
    margin-bottom: $spacing-xl;

    strong {
      color: $error-color;
    }
  }
}

// React Select custom styles
:global(.react-select__control) {
  @include input-base;
  border: 1px solid $border-color !important;
  box-shadow: none !important;
  min-height: 40px;

  &:hover {
    border-color: $primary-color !important;
  }
}

:global(.react-select__control--is-focused) {
  border-color: $primary-color !important;
  box-shadow: 0 0 0 2px rgba($primary-color, 0.1) !important;
}

:global(.react-select__value-container) {
  padding: 0 $spacing-sm;
}

:global(.react-select__placeholder) {
  color: $text-placeholder;
}

:global(.react-select__single-value) {
  color: $text-primary;
}

:global(.react-select__menu) {
  border: 1px solid $border-color;
  box-shadow: $shadow-md;
  z-index: 9999;
}

:global(.react-select__option) {
}

:global(.react-select__option:hover) {
  background-color: $background-hover;
}

:global(.react-select__option--is-selected) {
  background-color: $primary-color;
}

:global(.react-select__option--is-focused) {
  background-color: $primary-light;
}

// Loading, error, and helper text styles
.loadingText {
  font-size: $font-size-sm;
  color: $gray-600;
  margin-top: $spacing-1;
  font-style: italic;
}

.errorState {
  @include flex-between;
  align-items: center;
  margin-top: $spacing-1;
  gap: $spacing-2;
}

.errorText {
  font-size: $font-size-sm;
  color: $error-color;
}

.retryButton {
  font-size: $font-size-xs;
  padding: $spacing-1 $spacing-2;
  min-height: auto;
}

.helperText {
  font-size: $font-size-sm;
  color: $gray-600;
  margin-top: $spacing-1;
}

// Responsive design
@media (max-width: 768px) {
  .variantDisplay {
    flex-direction: column;
    gap: $spacing-md;
  }

  .variantActions {
    align-self: flex-end;
  }

  .variantDetails {
    grid-template-columns: 1fr;
  }
}
