# Generated by Django 5.2.5 on 2025-09-29 03:42

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0004_add_shared_product_images'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='productvariantimagelink',
            name='unique_variant_shared_image',
        ),
        migrations.RemoveConstraint(
            model_name='productvariantimagelink',
            name='unique_variant_order_active',
        ),
        migrations.AlterUniqueTogether(
            name='productvariantimagelink',
            unique_together={('product_variant', 'order', 'is_active'), ('product_variant', 'shared_image')},
        ),
    ]
