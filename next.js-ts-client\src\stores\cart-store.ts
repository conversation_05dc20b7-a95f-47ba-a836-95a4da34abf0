import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { createJSONStorage } from 'zustand/middleware'
import { CartStoreShape, ProductVariant } from '../types/store-types'

// {
//   title: title,
//   product_image: `https://res.cloudinary.com/dev-kani/${item.product_image[0]?.image}`,
//   quantity: 1,
//   price: item.price,
//   sku: item.sku
// }

const cartStore = create<CartStoreShape>()(
  persist(
    (set, get) => ({
      cartId: null,
      selectedVariant: null,
      cartItem: {
        id: null,
        product_id: null,
        product_variant: null,
        quantity: null,
        extra_data: {},
      },
      customer: null,
      selectedAddress: null,
      // paymentOptions: [], // unused
      selectedPaymentOption: null,

      // Selection state (persist-safe)
      selectedItemIds: [],
      selectAllItems: false,

      setCustomer: (customer) => set({ customer: customer }),
      setSelectedAddress: (address) => set({ selectedAddress: address }),
      // setPaymentOptions: (option) => set({ paymentOptions: option }),
      setSelectedPaymentOption: (paymentOption) =>
        set({ selectedPaymentOption: paymentOption }),

      setCartId: (newCartId) => set({ cartId: newCartId }),
      // setCartItems: (cartItemData) => set({ selectedVariant: cartItemData }),

      setCartItemId: (id: number) =>
        set((state) => ({
          cartItem: { ...state.cartItem, id },
        })),

      setProductId: (product_id: number) =>
        set((state) => ({
          cartItem: { ...state.cartItem, product_id },
        })),

      setCartItemProductVariant: (product_variant: number) =>
        set((state) => ({
          cartItem: { ...state.cartItem, product_variant },
        })),

      setProductVariant: (product_variant: ProductVariant) =>
        set({ selectedVariant: product_variant }),

      setQuantity: (quantity: number) =>
        set((state) => ({
          cartItem: { ...state.cartItem, quantity },
        })),
      setExtraData: (extra_data) =>
        set((state) => ({
          cartItem: { ...state.cartItem, extra_data },
        })),

      resetExtraData: () =>
        set((state) => ({
          cartItem: { ...state.cartItem, extra_data: {} },
        })),

      // Selection methods using serializable array
      toggleItemSelection: (itemId: number) =>
        set((state) => {
          const current = Array.isArray(state.selectedItemIds)
            ? state.selectedItemIds
            : []
          const setCopy = new Set(current)
          if (setCopy.has(itemId)) setCopy.delete(itemId)
          else setCopy.add(itemId)
          return { selectedItemIds: Array.from(setCopy) }
        }),

      toggleSelectAll: (allItemIds: number[]) =>
        set((state) => {
          const current = Array.isArray(state.selectedItemIds)
            ? state.selectedItemIds
            : []
          const allSelected = allItemIds.every((id) => current.includes(id))
          if (allSelected) {
            // Deselect all
            return {
              selectedItemIds: [],
              selectAllItems: false,
            }
          } else {
            // Select all
            return {
              selectedItemIds: allItemIds,
              selectAllItems: true,
            }
          }
        }),

      clearSelection: () =>
        set({
          selectedItemIds: [],
          selectAllItems: false,
        }),

      setSelectedItems: (itemIds: number[]) =>
        set({
          selectedItemIds: itemIds,
        }),

      isItemSelected: (itemId: number) => {
        const state = get()
        return Array.isArray(state.selectedItemIds)
          ? state.selectedItemIds.includes(itemId)
          : false
      },
    }),
    {
      name: 'cart_store',
      storage: createJSONStorage(() => localStorage),
      version: 1,
      migrate: (persistedState: unknown, _ver: number) => {
        // reference param to satisfy linter
        void _ver
        // Migrate old Set-based `selectedCartItems` if present
        if (!persistedState || typeof persistedState !== 'object')
          return persistedState

        const ps = persistedState as Record<string, unknown>
        if (ps.selectedCartItems) {
          const old = ps.selectedCartItems
          let selectedItemIds: number[]
          if (Array.isArray(old)) selectedItemIds = old as number[]
          else if (
            typeof old === 'object' &&
            old !== null &&
            Array.isArray((old as Record<string, unknown>).data)
          ) {
            selectedItemIds = (
              (old as Record<string, unknown>).data as unknown[]
            ).map((v) => Number(v))
          } else if (Symbol.iterator in Object(old)) {
            selectedItemIds = Array.from(old as Iterable<number>)
          } else {
            selectedItemIds = []
          }
          return { ...ps, selectedItemIds }
        }
        return ps
      },
      partialize: (state) => ({
        cartId: state.cartId,
        selectedItemIds: state.selectedItemIds,
      }),
    }
  )
)

export default cartStore
