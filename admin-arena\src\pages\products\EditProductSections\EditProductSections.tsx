// Edit Product Sections page for comprehensive product editing
// Handles Product details, Variants, Images, and Attribute Value associations

import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from '@tanstack/react-router'
import { FiArrowLeft, FiPackage, FiLayers, FiImage, FiTag } from 'react-icons/fi'
import { useFullProductDetails } from '../../../hooks/use-products'
import { Card, CardHeader, CardBody } from '../../../components/ui/Card'
import { Button } from '../../../components/ui/Button'
import { PageLoading } from '../../../components/ui/LoadingSpinner'
import { ProductDetailsSection } from './sections/ProductDetailsSection'
import { ProductVariantsSection } from './sections/ProductVariantsSection'
import { ProductImagesSection } from './sections/ProductImagesSection'
import { AttributeValuesSection } from './sections/AttributeValuesSection'
import styles from './EditProductSections.module.scss'

type SectionType = 'details' | 'variants' | 'images' | 'attributes'

interface SectionConfig {
  id: SectionType
  title: string
  icon: React.ReactNode
  description: string
}

const sections: SectionConfig[] = [
  {
    id: 'details',
    title: 'Product Details',
    icon: <FiPackage />,
    description: 'Basic product information, category, brand, and settings'
  },
  {
    id: 'variants',
    title: 'Product Variants',
    icon: <FiLayers />,
    description: 'Product variants, pricing, SKUs, and inventory'
  },
  {
    id: 'images',
    title: 'Product Images',
    icon: <FiImage />,
    description: 'Product images for variants'
  },
  {
    id: 'attributes',
    title: 'Attribute Values',
    icon: <FiTag />,
    description: 'Attribute values associated with product variants'
  }
]

export const EditProductSections: React.FC = () => {
  const navigate = useNavigate()
  const { productId } = useParams({ from: '/products/$productId/edit' })
  const [activeSection, setActiveSection] = useState<SectionType>('details')
  const [selectedProductType, setSelectedProductType] = useState<number | undefined>(undefined)

  const { data: product, isLoading, error } = useFullProductDetails(parseInt(productId))

  // Initialize selectedProductType with the current product's product_type
  useEffect(() => {
    if (product?.product_type) {
      setSelectedProductType(product.product_type)
    }
  }, [product])

  // Scroll to section function
  const scrollToSection = (sectionId: SectionType) => {
    setActiveSection(sectionId)
    const element = document.getElementById(`section-${sectionId}`)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    }
  }

  // Intersection observer to update active section based on scroll position
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -70% 0px',
      threshold: 0
    }

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id.replace('section-', '') as SectionType
          setActiveSection(sectionId)
        }
      })
    }

    const observer = new IntersectionObserver(observerCallback, observerOptions)

    // Observe all sections
    const sections = ['details', 'variants', 'images', 'attributes']
    sections.forEach(sectionId => {
      const element = document.getElementById(`section-${sectionId}`)
      if (element) {
        observer.observe(element)
      }
    })

    return () => {
      observer.disconnect()
    }
  }, [product]) // Re-run when product data changes

  if (isLoading) {
    return <PageLoading message="Loading product..." />
  }

  if (error) {
    return (
      <div className={styles.error}>
        <h2>Error Loading Product</h2>
        <p>{error.message}</p>
        <Button onClick={() => navigate({ to: '/products' })}>
          Back to Products
        </Button>
      </div>
    )
  }

  if (!product) {
    return (
      <div className={styles.error}>
        <h2>Product Not Found</h2>
        <p>The requested product could not be found.</p>
        <Button onClick={() => navigate({ to: '/products' })}>
          Back to Products
        </Button>
      </div>
    )
  }

  const renderAllSections = () => {
    return (
      <div className={styles.sectionsContainer}>
        <div id="section-details" className={styles.section}>
          <ProductDetailsSection 
            product={product} 
            onProductTypeChange={setSelectedProductType} 
          />
        </div>
        <div id="section-variants" className={styles.section}>
          <ProductVariantsSection 
            product={product} 
            productType={selectedProductType} 
          />
        </div>
        <div id="section-images" className={styles.section}>
          <ProductImagesSection product={product} />
        </div>
        <div id="section-attributes" className={styles.section}>
          <AttributeValuesSection product={product} />
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Button
            variant="ghost"
            onClick={() => navigate({ to: '/products' })}
            className={styles.backButton}
          >
            <FiArrowLeft />
            Back to Products
          </Button>

          <div className={styles.titleSection}>
            <h1 className={styles.title}>Edit Product</h1>
            <p className={styles.subtitle}>
              {product.title}
            </p>
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.sidebar}>
          <Card className={styles.sidebarCard}>
            <CardHeader>
              <h3>Sections</h3>
            </CardHeader>
            <CardBody>
              <nav className={styles.sectionNav}>
                {sections.map((section) => (
                  <button
                    key={section.id}
                    className={`${styles.sectionButton} ${activeSection === section.id ? styles.active : ''
                      }`}
                    onClick={() => scrollToSection(section.id)}
                  >
                    <div className={styles.sectionIcon}>
                      {section.icon}
                    </div>
                    <div className={styles.sectionInfo}>
                      <div className={styles.sectionTitle}>
                        {section.title}
                      </div>
                      <div className={styles.sectionDescription}>
                        {section.description}
                      </div>
                    </div>
                  </button>
                ))}
              </nav>
            </CardBody>
          </Card>
        </div>

        <div className={styles.mainContent}>
          {renderAllSections()}
        </div>
      </div>
    </div>
  )
}

export default EditProductSections
