// Enhanced Product Images Section with Shared Image Support
// Handles direct images, shared images, and linking functionality

import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  FiUpload,
  FiEdit,
  FiTrash2,
  FiImage,
  FiX,
  FiSave,
  FiEye,
  FiLink,
  FiShare2,
} from 'react-icons/fi'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import {
  useProductImages,
  useUploadProductImage,
  useDeleteProductImage,
  useReorderProductImagesDragDrop,
} from '../../../../hooks/products-hooks/use-product-images'
import {
  useSharedProductImages,
  useCombinedProductImages,
  useUploadSharedProductImage,
  useDeleteSharedProductImage,
  useLinkSharedImageToVariant,
  useUnlinkSharedImageFromVariant,
  useConvertImageToShared,
  useConvertSharedImageToDirect,
} from '../../../../hooks/products-hooks/use-shared-images'
import { Card, CardHeader, CardBody } from '../../../../components/ui/Card'
import { Button } from '../../../../components/ui/Button'
import { Input } from '../../../../components/ui/Input'
import { Modal } from '../../../../components/ui/Modal'
import {
  ButtonLoading,
  PageLoading,
} from '../../../../components/ui/LoadingSpinner'
import { Badge } from '../../../../components/ui/Badge'
import { DragIndicator } from '../../../../components/ui/DragIndicator'
import type {
  Product,
  ProductVariant,
  ProductImage,
  SharedProductImage,
  CombinedProductImage,
} from '../../../../types/api-types'
import styles from './ProductImagesSection.module.scss'

const imageUploadSchema = z.object({
  alternative_text: z.string().min(1, 'Alt text is required'),
  image: z.instanceof(File, { message: 'Image file is required' }),
})

const sharedImageUploadSchema = z.object({
  alternative_text: z.string().min(1, 'Alt text is required'),
  image: z.instanceof(File, { message: 'Image file is required' }),
})

type ImageUploadFormData = z.infer<typeof imageUploadSchema>
type SharedImageUploadFormData = z.infer<typeof sharedImageUploadSchema>

interface EnhancedProductImagesSectionProps {
  product: Product
}

type TabType = 'variant-images' | 'shared-images' | 'link-images'

const DND_ITEM_TYPE = 'IMAGE'

export const EnhancedProductImagesSection: React.FC<
  EnhancedProductImagesSectionProps
> = ({ product }) => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(
    null
  )
  const [activeTab, setActiveTab] = useState<TabType>('variant-images')
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [isSharedUploadModalOpen, setIsSharedUploadModalOpen] = useState(false)
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [editingImage, setEditingImage] = useState<ProductImage | null>(null)
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  // Hooks for direct images
  const { data: directImages, isLoading: directImagesLoading } =
    useProductImages(selectedVariant?.id || 0)
  const uploadDirectImageMutation = useUploadProductImage()
  const deleteDirectImageMutation = useDeleteProductImage()
  const reorderDirectImagesMutation = useReorderProductImagesDragDrop()

  // Hooks for shared images
  const { data: sharedImages, isLoading: sharedImagesLoading } =
    useSharedProductImages(product.id)
  const { data: combinedImagesData, isLoading: combinedImagesLoading } =
    useCombinedProductImages(selectedVariant?.id || 0)
  const uploadSharedImageMutation = useUploadSharedProductImage()
  const deleteSharedImageMutation = useDeleteSharedProductImage()
  const linkSharedImageMutation = useLinkSharedImageToVariant()
  const unlinkSharedImageMutation = useUnlinkSharedImageFromVariant()
  const convertToSharedMutation = useConvertImageToShared()
  const convertToDirectMutation = useConvertSharedImageToDirect()

  // Form for direct image upload
  const directImageForm = useForm<ImageUploadFormData>({
    resolver: zodResolver(imageUploadSchema),
  })

  // Form for shared image upload
  const sharedImageForm = useForm<SharedImageUploadFormData>({
    resolver: zodResolver(sharedImageUploadSchema),
  })

  // Dropzone for direct images
  const directImageDropzone = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    multiple: false,
    onDrop: useCallback(
      (acceptedFiles: File[]) => {
        if (acceptedFiles.length > 0) {
          directImageForm.setValue('image', acceptedFiles[0])
          setIsUploadModalOpen(true)
        }
      },
      [directImageForm]
    ),
  })

  // Dropzone for shared images
  const sharedImageDropzone = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    multiple: false,
    onDrop: useCallback(
      (acceptedFiles: File[]) => {
        if (acceptedFiles.length > 0) {
          sharedImageForm.setValue('image', acceptedFiles[0])
          setIsSharedUploadModalOpen(true)
        }
      },
      [sharedImageForm]
    ),
  })

  // Handle direct image upload
  const handleDirectImageUpload = useCallback(
    async (data: ImageUploadFormData) => {
      if (!selectedVariant) return

      try {
        await uploadDirectImageMutation.mutateAsync({
          image: data.image,
          alternative_text: data.alternative_text,
          product_variant: selectedVariant.id,
        })
        setIsUploadModalOpen(false)
        directImageForm.reset()
      } catch (error) {
        console.error('Failed to upload direct image:', error)
      }
    },
    [selectedVariant, uploadDirectImageMutation, directImageForm]
  )

  // Handle shared image upload
  const handleSharedImageUpload = useCallback(
    async (data: SharedImageUploadFormData) => {
      try {
        await uploadSharedImageMutation.mutateAsync({
          image: data.image,
          alternative_text: data.alternative_text,
          product: product.id,
          is_active: true,
        })
        setIsSharedUploadModalOpen(false)
        sharedImageForm.reset()
      } catch (error) {
        console.error('Failed to upload shared image:', error)
      }
    },
    [product.id, uploadSharedImageMutation, sharedImageForm]
  )

  // Handle linking shared image to variant
  const handleLinkSharedImage = useCallback(
    async (sharedImageId: number) => {
      if (!selectedVariant) return

      try {
        // Get next order for this variant
        const maxOrder = combinedImagesData?.images?.length || 0

        await linkSharedImageMutation.mutateAsync({
          product_variant: selectedVariant.id,
          shared_image_id: sharedImageId,
          order: maxOrder + 1,
          is_active: true,
        })
        setIsLinkModalOpen(false)
      } catch (error) {
        console.error('Failed to link shared image:', error)
      }
    },
    [selectedVariant, combinedImagesData, linkSharedImageMutation]
  )

  // Handle unlinking shared image from variant
  const handleUnlinkSharedImage = useCallback(
    async (linkId: number) => {
      try {
        await unlinkSharedImageMutation.mutateAsync(linkId)
      } catch (error) {
        console.error('Failed to unlink shared image:', error)
      }
    },
    [unlinkSharedImageMutation]
  )

  // Handle converting direct image to shared
  const handleConvertToShared = useCallback(
    async (imageId: number) => {
      try {
        await convertToSharedMutation.mutateAsync({
          imageId,
          data: { link_to_variants: [] },
        })
      } catch (error) {
        console.error('Failed to convert to shared image:', error)
      }
    },
    [convertToSharedMutation]
  )

  // Handle converting shared image to direct
  const handleConvertToDirect = useCallback(
    async (sharedImageId: number) => {
      if (!selectedVariant) return

      try {
        await convertToDirectMutation.mutateAsync({
          sharedImageId,
          variantId: selectedVariant.id,
        })
      } catch (error) {
        console.error('Failed to convert to direct image:', error)
      }
    },
    [selectedVariant, convertToDirectMutation]
  )

  // Handle image deletion
  const handleDeleteImage = useCallback(
    async (imageId: number, imageType: 'direct' | 'shared') => {
      try {
        if (imageType === 'direct') {
          await deleteDirectImageMutation.mutateAsync(imageId)
        } else {
          await deleteSharedImageMutation.mutateAsync(imageId)
        }
      } catch (error) {
        console.error('Failed to delete image:', error)
      }
    },
    [deleteDirectImageMutation, deleteSharedImageMutation]
  )

  // Get current images based on active tab
  const getCurrentImages = () => {
    switch (activeTab) {
      case 'variant-images':
        return directImages || []
      case 'shared-images':
        return sharedImages || []
      case 'link-images':
        return combinedImagesData?.images || []
      default:
        return []
    }
  }

  const isLoading =
    directImagesLoading || sharedImagesLoading || combinedImagesLoading

  if (isLoading) {
    return <PageLoading message='Loading images...' />
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Card className={styles.productImagesSection}>
        <CardHeader>
          <div className={styles.header}>
            <h3>Product Images</h3>
            <div className={styles.variantSelector}>
              <label>Select Variant:</label>
              <select
                value={selectedVariant?.id || ''}
                onChange={(e) => {
                  const variantId = parseInt(e.target.value)
                  const variant =
                    product.variants?.find((v) => v.id === variantId) || null
                  setSelectedVariant(variant)
                }}
                className={styles.variantSelect}
              >
                <option value=''>Select a variant</option>
                {product.variants?.map((variant) => (
                  <option key={variant.id} value={variant.id}>
                    {variant.sku} - {variant.title || 'No title'}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {selectedVariant && (
            <div className={styles.tabNavigation}>
              <button
                className={`${styles.tab} ${activeTab === 'variant-images' ? styles.active : ''}`}
                onClick={() => setActiveTab('variant-images')}
              >
                <FiImage /> Variant Images
              </button>
              <button
                className={`${styles.tab} ${activeTab === 'shared-images' ? styles.active : ''}`}
                onClick={() => setActiveTab('shared-images')}
              >
                <FiShare2 /> Shared Images
              </button>
              <button
                className={`${styles.tab} ${activeTab === 'link-images' ? styles.active : ''}`}
                onClick={() => setActiveTab('link-images')}
              >
                <FiLink /> Combined View
              </button>
            </div>
          )}
        </CardHeader>

        <CardBody>
          {!selectedVariant ? (
            <div className={styles.noVariantSelected}>
              <FiImage size={48} />
              <p>Please select a variant to manage images</p>
            </div>
          ) : (
            <div className={styles.tabContent}>
              {/* Variant Images Tab */}
              {activeTab === 'variant-images' && (
                <div className={styles.variantImagesTab}>
                  <div className={styles.uploadSection}>
                    <div
                      {...directImageDropzone.getRootProps()}
                      className={`${styles.dropzone} ${directImageDropzone.isDragActive ? styles.dragActive : ''}`}
                    >
                      <input {...directImageDropzone.getInputProps()} />
                      <FiUpload size={24} />
                      <p>Drop images here or click to upload</p>
                      <p className={styles.hint}>
                        Supports: JPEG, PNG, GIF, WebP
                      </p>
                    </div>
                    <Button
                      variant='primary'
                      onClick={() => setIsUploadModalOpen(true)}
                      disabled={!selectedVariant}
                    >
                      <FiUpload /> Upload Direct Image
                    </Button>
                  </div>

                  <div className={styles.imagesGrid}>
                    {directImages?.map((image) => (
                      <div key={image.id} className={styles.imageCard}>
                        <div className={styles.imagePreview}>
                          <img src={image.image} alt={image.alternative_text} />
                          <div className={styles.imageOverlay}>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => setPreviewImage(image.image)}
                            >
                              <FiEye />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleConvertToShared(image.id)}
                              title='Convert to shared image'
                            >
                              <FiShare2 />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleDeleteImage(image.id, 'direct')
                              }
                              className={styles.deleteButton}
                            >
                              <FiTrash2 />
                            </Button>
                          </div>
                        </div>
                        <div className={styles.imageInfo}>
                          <p className={styles.altText}>
                            {image.alternative_text}
                          </p>
                          <Badge variant='secondary'>
                            Order: {image.order}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Shared Images Tab */}
              {activeTab === 'shared-images' && (
                <div className={styles.sharedImagesTab}>
                  <div className={styles.uploadSection}>
                    <div
                      {...sharedImageDropzone.getRootProps()}
                      className={`${styles.dropzone} ${sharedImageDropzone.isDragActive ? styles.dragActive : ''}`}
                    >
                      <input {...sharedImageDropzone.getInputProps()} />
                      <FiUpload size={24} />
                      <p>Drop shared images here or click to upload</p>
                      <p className={styles.hint}>
                        These images can be linked to multiple variants
                      </p>
                    </div>
                    <Button
                      variant='primary'
                      onClick={() => setIsSharedUploadModalOpen(true)}
                    >
                      <FiShare2 /> Upload Shared Image
                    </Button>
                  </div>

                  <div className={styles.imagesGrid}>
                    {sharedImages?.map((image) => (
                      <div key={image.id} className={styles.imageCard}>
                        <div className={styles.imagePreview}>
                          <img src={image.image} alt={image.alternative_text} />
                          <div className={styles.imageOverlay}>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => setPreviewImage(image.image)}
                            >
                              <FiEye />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => handleLinkSharedImage(image.id)}
                              title='Link to current variant'
                              disabled={!selectedVariant}
                            >
                              <FiLink />
                            </Button>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() =>
                                handleDeleteImage(image.id, 'shared')
                              }
                              className={styles.deleteButton}
                            >
                              <FiTrash2 />
                            </Button>
                          </div>
                        </div>
                        <div className={styles.imageInfo}>
                          <p className={styles.altText}>
                            {image.alternative_text}
                          </p>
                          <Badge variant='info'>
                            Linked to {image.linked_variants_count} variants
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Combined Images Tab */}
              {activeTab === 'link-images' && (
                <div className={styles.combinedImagesTab}>
                  <div className={styles.tabHeader}>
                    <h4>Combined View - {selectedVariant?.sku}</h4>
                    <Button
                      variant='secondary'
                      onClick={() => setIsLinkModalOpen(true)}
                    >
                      <FiLink /> Link Shared Image
                    </Button>
                  </div>

                  <div className={styles.imagesGrid}>
                    {combinedImagesData?.images?.map((image) => (
                      <div
                        key={`${image.image_type}-${image.id}`}
                        className={styles.imageCard}
                      >
                        <div className={styles.imagePreview}>
                          <img src={image.image} alt={image.alternative_text} />
                          <div className={styles.imageOverlay}>
                            <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => setPreviewImage(image.image)}
                            >
                              <FiEye />
                            </Button>
                            {image.image_type === 'shared' && image.link_id && (
                              <Button
                                variant='ghost'
                                size='sm'
                                onClick={() =>
                                  handleUnlinkSharedImage(image.link_id!)
                                }
                                title='Unlink from this variant'
                              >
                                <FiLink />
                              </Button>
                            )}
                            {image.image_type === 'shared' &&
                              image.shared_image_id && (
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() =>
                                    handleConvertToDirect(
                                      image.shared_image_id!
                                    )
                                  }
                                  title='Convert to direct image'
                                >
                                  <FiImage />
                                </Button>
                              )}
                          </div>
                        </div>
                        <div className={styles.imageInfo}>
                          <p className={styles.altText}>
                            {image.alternative_text}
                          </p>
                          <div className={styles.imageBadges}>
                            <Badge
                              variant={
                                image.image_type === 'direct'
                                  ? 'primary'
                                  : 'secondary'
                              }
                            >
                              {image.image_type === 'direct'
                                ? 'Direct'
                                : 'Shared'}
                            </Badge>
                            <Badge variant='outline'>
                              Order: {image.order}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Direct Image Upload Modal */}
      <Modal
        isOpen={isUploadModalOpen}
        onClose={() => {
          setIsUploadModalOpen(false)
          directImageForm.reset()
        }}
        title='Upload Direct Image'
        size='md'
      >
        <form onSubmit={directImageForm.handleSubmit(handleDirectImageUpload)}>
          <div className={styles.modalContent}>
            <div className={styles.formGroup}>
              <label>Alternative Text</label>
              <Controller
                name='alternative_text'
                control={directImageForm.control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    placeholder='Describe the image for accessibility'
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Image File</label>
              <Controller
                name='image'
                control={directImageForm.control}
                render={({
                  field: { onChange, value, ...field },
                  fieldState,
                }) => (
                  <div>
                    <input
                      {...field}
                      type='file'
                      accept='image/*'
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) onChange(file)
                      }}
                    />
                    {fieldState.error && (
                      <p className={styles.error}>{fieldState.error.message}</p>
                    )}
                  </div>
                )}
              />
            </div>
          </div>

          <div className={styles.modalActions}>
            <Button
              type='button'
              variant='secondary'
              onClick={() => {
                setIsUploadModalOpen(false)
                directImageForm.reset()
              }}
            >
              Cancel
            </Button>
            <ButtonLoading
              type='submit'
              variant='primary'
              loading={uploadDirectImageMutation.isPending}
            >
              Upload Image
            </ButtonLoading>
          </div>
        </form>
      </Modal>

      {/* Shared Image Upload Modal */}
      <Modal
        isOpen={isSharedUploadModalOpen}
        onClose={() => {
          setIsSharedUploadModalOpen(false)
          sharedImageForm.reset()
        }}
        title='Upload Shared Image'
        size='md'
      >
        <form onSubmit={sharedImageForm.handleSubmit(handleSharedImageUpload)}>
          <div className={styles.modalContent}>
            <div className={styles.formGroup}>
              <label>Alternative Text</label>
              <Controller
                name='alternative_text'
                control={sharedImageForm.control}
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    placeholder='Describe the image for accessibility'
                    error={fieldState.error?.message}
                  />
                )}
              />
            </div>

            <div className={styles.formGroup}>
              <label>Image File</label>
              <Controller
                name='image'
                control={sharedImageForm.control}
                render={({
                  field: { onChange, value, ...field },
                  fieldState,
                }) => (
                  <div>
                    <input
                      {...field}
                      type='file'
                      accept='image/*'
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) onChange(file)
                      }}
                    />
                    {fieldState.error && (
                      <p className={styles.error}>{fieldState.error.message}</p>
                    )}
                  </div>
                )}
              />
            </div>
          </div>

          <div className={styles.modalActions}>
            <Button
              type='button'
              variant='secondary'
              onClick={() => {
                setIsSharedUploadModalOpen(false)
                sharedImageForm.reset()
              }}
            >
              Cancel
            </Button>
            <ButtonLoading
              type='submit'
              variant='primary'
              loading={uploadSharedImageMutation.isPending}
            >
              Upload Shared Image
            </ButtonLoading>
          </div>
        </form>
      </Modal>

      {/* Link Shared Image Modal */}
      <Modal
        isOpen={isLinkModalOpen}
        onClose={() => setIsLinkModalOpen(false)}
        title='Link Shared Image to Variant'
        size='lg'
      >
        <div className={styles.modalContent}>
          <p>
            Select a shared image to link to{' '}
            <strong>{selectedVariant?.sku}</strong>:
          </p>

          <div className={styles.sharedImagesGrid}>
            {sharedImages?.map((image) => (
              <div key={image.id} className={styles.linkableImageCard}>
                <div className={styles.imagePreview}>
                  <img src={image.image} alt={image.alternative_text} />
                </div>
                <div className={styles.imageInfo}>
                  <p className={styles.altText}>{image.alternative_text}</p>
                  <Badge variant='info'>
                    Linked to {image.linked_variants_count} variants
                  </Badge>
                </div>
                <Button
                  variant='primary'
                  size='sm'
                  onClick={() => handleLinkSharedImage(image.id)}
                  loading={linkSharedImageMutation.isPending}
                >
                  <FiLink /> Link
                </Button>
              </div>
            ))}
          </div>

          {(!sharedImages || sharedImages.length === 0) && (
            <div className={styles.noSharedImages}>
              <FiShare2 size={48} />
              <p>No shared images available</p>
              <p>Upload shared images first to link them to variants</p>
            </div>
          )}
        </div>

        <div className={styles.modalActions}>
          <Button
            type='button'
            variant='secondary'
            onClick={() => setIsLinkModalOpen(false)}
          >
            Cancel
          </Button>
        </div>
      </Modal>

      {/* Image Preview Modal */}
      {previewImage && (
        <Modal
          isOpen={!!previewImage}
          onClose={() => setPreviewImage(null)}
          title='Image Preview'
          size='xl'
        >
          <div className={styles.imagePreviewModal}>
            <img src={previewImage} alt='Preview' />
          </div>
          <div className={styles.modalActions}>
            <Button variant='secondary' onClick={() => setPreviewImage(null)}>
              Close
            </Button>
          </div>
        </Modal>
      )}
    </DndProvider>
  )
}
