'use client'

import Alert from '@/src/components/utils/alert/Alert'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import { useSimpleCart, useShippingCalculation } from '@/src/hooks/cart-hooks'
import { usePaymentOptions } from '@/src/hooks/checkout-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { useCreateOrder } from '@/src/hooks/order-hooks'
import authStore from '@/src/stores/auth-store'
import cartStore from '@/src/stores/cart-store'
import { PaymentOptionsShape, CartShape } from '@/src/types/types'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'
import styles from './PaymentChoice.module.scss'
import ButtonState from '@/src/components/utils/button-state/ButtonState'
import Spinner from '@/src/components/utils/spinner/Spinner'
import SelectedCartSummary from '../../components/cart/SelectedCartSummary'
import CartItemsList from '../../components/cart/CartItemsList'

const PaymentChoice = () => {
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const {
    cartId,
    setSelectedPaymentOption,
    selectedAddress,
    selectedPaymentOption,
  } = cartStore()
  const { createOrder } = useCreateOrder()
  const payOptions = usePaymentOptions()
  const shippingCalculation = useShippingCalculation()

  // Use simple cart for selection state, shipping calculation for pricing
  const simpleCartQuery = useSimpleCart()
  const cartData = simpleCartQuery.data // Always use simple cart for selection state
  const pricingData = shippingCalculation.data?.cart || simpleCartQuery.data // Use shipping calc for pricing when available
  const isLoading = simpleCartQuery.isLoading || shippingCalculation.isPending
  const error = simpleCartQuery.error || shippingCalculation.error

  // derive selected ids directly from simple cart data (server = source of truth)
  const serverSelectedIds = React.useMemo(() => {
    const items = Array.isArray(cartData?.cart_items) ? cartData.cart_items : []
    return new Set<number>(
      items.filter((it) => it?.is_selected === true).map((it) => Number(it.id))
    )
  }, [cartData?.cart_items])

  // Type guard to check if data has shipping information
  const hasShippingData = (data: any): data is CartShape => {
    return data && 'shipping_cost' in data
  }

  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner
  // createOrder.isPending = true

  console.log(payOptions.data)

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router])

  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {
    setSelectedPaymentOption(paymentOption)
  }

  useEffect(() => {
    if (
      payOptions.data &&
      payOptions.data.length > 0 &&
      !selectedPaymentOption
    ) {
      setSelectedPaymentOption(payOptions.data[0])
    }
  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])

  // Calculate shipping when component loads and when selected address changes
  useEffect(() => {
    if (
      cartId &&
      selectedAddress?.id &&
      cartData &&
      cartData.cart_items?.length > 0
    ) {
      // Only calculate if shipping hasn't been calculated yet
      if (!hasShippingData(pricingData) && !shippingCalculation.isPending) {
        shippingCalculation.calculateShipping({
          destination_address_id: selectedAddress.id,
          get_all_options: false,
        })
      }
    }
  }, [
    cartId,
    selectedAddress?.id,
    cartData?.cart_items?.length,
    shippingCalculation.isPending, // Add this to prevent concurrent calls
    shippingCalculation.calculateShipping,
    pricingData,
    shippingCalculation,
  ])

  // useEffect(() => {
  //   if (shippingCalculation.isSuccess && cartId) {
  //     refetch()
  //   }
  // }, [shippingCalculation.isSuccess, cartId, refetch])

  console.log(customer)
  console.log(selectedAddress)
  console.log(selectedPaymentOption)

  // Testing of shipping calculation Pending state (leftover test code removed)

  const createOrderFn = () => {
    if (
      window.confirm(
        'Payment Method or other order details cannot be changed after placing the order.\n' +
          'Make sure you have selected the correct payment method and other order details before placing the order.\n' +
          'Click OK to place the order or Cancel to go back and make changes.'
      )
    ) {
      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {
        // Check if shipping calculation is required
        if (!hasShippingData(pricingData)) {
          if (shippingCalculation.isPending) {
            alert(
              'Shipping calculation is in progress. Please wait a moment and try again.'
            )
            return
          } else {
            // Calculate shipping first
            shippingCalculation.calculateShipping({
              destination_address_id: selectedAddress.id,
              get_all_options: false,
            })
            alert('Calculating shipping costs, please try again in a moment.')
            return
          }
        }

        // Shipping is calculated, proceed with order creation
        // Use the already computed serverSelectedIds from the component state

        const selectedItemIdsArray: number[] = Array.from(
          serverSelectedIds
        ).map((id) => Number(id))

        createOrder.mutate(
          {
            cart_id: cartId!,
            delivery_status: 'Pending',
            selected_address: selectedAddress.id,
            payment_method: selectedPaymentOption.id,
            selected_cart_item_ids:
              selectedItemIdsArray && selectedItemIdsArray.length > 0
                ? selectedItemIdsArray
                : undefined,
          },
          {
            onSuccess: (data) => {
              router.push(`/checkout/order/${data.id}`)
            },
          }
        )
      }
    }
  }

  return (
    <>
      {!cartId ? (
        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />
      ) : isLoading ? (
        <Spinner loading={true} size={20} color='#0091CF' spinnerType='clip' />
      ) : error ? (
        <Alert variant='error' message={error.message} />
      ) : !cartData ||
        cartData?.cart_items?.length === 0 ||
        Object.keys(cartData).length === 0 ? (
        <div className={styles.empty_cart}>
          <p>Your cart is empty. Add some products to the cart to checkout!</p>
          <Link href='/'>Go Shopping </Link>
        </div>
      ) : (
        <>
          <div className='container'>
            <h3 className={styles.place_order}>Place Order</h3>

            {shippingCalculation.isPending && (
              <Alert variant='info' message='Calculating shipping costs...' />
            )}

            {hasShippingData(pricingData) && pricingData.shipping_cost && (
              <Alert
                variant='success'
                message={`Shipping cost calculated successfully! Estimated delivery: ${
                  pricingData.packing_details?.calculated_at
                    ? new Date(
                        pricingData.packing_details.calculated_at
                      ).toLocaleDateString()
                    : 'N/A'
                }`}
              />
            )}

            <div className={styles.payment_options_stage}>
              <section>
                <section className={styles.contact_info}>
                  <div className={styles.contact_details}>
                    <h3>Contact Details: </h3>
                    <p>
                      Deliver to: {customer?.first_name} {customer?.last_name}
                    </p>
                    <p>Phone: {customer?.phone_number}</p>
                    <p>Email to: {customer?.email}</p>
                  </div>
                  <div className={styles.shipping_address}>
                    <h3>Shipping address: </h3>
                    <address>
                      {selectedAddress?.full_name},<br />
                      {selectedAddress?.street_name},<br />
                      {selectedAddress?.postal_code},<br />
                      {selectedAddress?.city_or_village}
                      <br />
                    </address>
                  </div>
                </section>
                <hr />
                <div className={styles.cart}>
                  <CartItemsList
                    cartItems={cartData.cart_items}
                    selectedIds={serverSelectedIds}
                    showSelection={false}
                  />
                </div>
                <hr />

                {shippingCalculation.error && (
                  <div>
                    <Alert
                      variant='error'
                      message={`Shipping calculation failed: ${shippingCalculation.error.message}`}
                    />
                    <button
                      className={`${styles.retry_shipping_calc} empty_btn`}
                      onClick={() =>
                        selectedAddress?.id &&
                        shippingCalculation.calculateShipping({
                          destination_address_id: selectedAddress.id,
                          get_all_options: false,
                        })
                      }
                      // style={{
                      //   marginTop: '10px',
                      //   padding: '8px 16px',
                      //   backgroundColor: '#0091CF',
                      //   color: 'white',
                      //   border: 'none',
                      //   borderRadius: '4px',
                      //   cursor: 'pointer',
                      // }}
                    >
                      Retry Shipping Calculation
                    </button>
                  </div>
                )}

                <section>
                  <div className={styles.payment_options}>
                    <h3>Payment Method:</h3>
                    {payOptions?.isPending ? (
                      <Alert
                        variant='info'
                        message='Payment options are loading'
                      />
                    ) : payOptions?.error ? (
                      <Alert
                        variant='error'
                        message={payOptions.error.message}
                      />
                    ) : payOptions?.data?.length === 0 ? (
                      <Alert
                        variant='error'
                        message='No payment options available'
                      />
                    ) : (
                      <>
                        {payOptions?.data?.map(
                          (option: PaymentOptionsShape) => (
                            <div key={option.id}>
                              <input
                                type='radio'
                                id={`payment-${option.id}`}
                                name='payment-option'
                                checked={
                                  selectedPaymentOption?.id === option.id
                                }
                                onChange={() =>
                                  handlePaymentOptionChange(option)
                                }
                              />
                              <label htmlFor={`payment-${option.id}`}>
                                {option.name}
                              </label>
                            </div>
                          )
                        )}
                      </>
                    )}
                  </div>
                </section>
              </section>
              <section className={styles.price_summary}>
                <SelectedCartSummary
                  cartItems={cartData.cart_items}
                  selectedIds={serverSelectedIds}
                  totalPrice={
                    hasShippingData(pricingData)
                      ? pricingData.total_price
                      : cartData?.selected_total_price
                  }
                  shippingCost={
                    hasShippingData(pricingData)
                      ? pricingData.shipping_cost
                      : undefined
                  }
                  packingCost={
                    hasShippingData(pricingData)
                      ? pricingData.packing_cost
                      : undefined
                  }
                  grandTotal={
                    hasShippingData(pricingData)
                      ? pricingData.grand_total
                      : undefined
                  }
                  item_count={
                    hasShippingData(pricingData)
                      ? pricingData.item_count
                      : cartData?.selected_item_count
                  }
                  cart_weight={
                    hasShippingData(pricingData)
                      ? pricingData.cart_weight
                      : cartData?.selected_cart_weight
                  }
                  isShippingCalculated={
                    hasShippingData(pricingData) && !!pricingData.shipping_cost
                  }
                />
                <button
                  type='submit'
                  disabled={
                    createOrder.isPending || shippingCalculation.isPending
                  }
                  onClick={createOrderFn}
                >
                  <ButtonState
                    isLoading={
                      createOrder.isPending || shippingCalculation.isPending
                    }
                    loadingText={
                      shippingCalculation.isPending
                        ? 'Calculating Shipping Cost...'
                        : 'Placing the Order...'
                    }
                    buttonText='Place Order'
                    spinnerSize={16}
                    spinnerColor='#fff'
                    spinnerType='clip'
                  />
                </button>
              </section>
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default PaymentChoice
