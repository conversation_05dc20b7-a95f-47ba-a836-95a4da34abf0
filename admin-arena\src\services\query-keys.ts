// TanStack Query keys factory for consistent caching
// Hierarchical key structure for efficient cache invalidation

import {
  OrderFilters,
  ProductFilters,
  CustomerFilters,
} from '../types/api-types'

export const queryKeys = {
  // Authentication
  auth: {
    all: ['auth'] as const,
    user: () => [...queryKeys.auth.all, 'user'] as const,
    permissions: () => [...queryKeys.auth.all, 'permissions'] as const,
    checkPermission: (permission: string) =>
      [...queryKeys.auth.permissions(), permission] as const,
  },

  // Orders
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (filters: OrderFilters) =>
      [...queryKeys.orders.lists(), filters] as const,
    details: () => [...queryKeys.orders.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.orders.details(), id] as const,
    assignments: () => [...queryKeys.orders.all, 'assignments'] as const,
    myAssignments: () => [...queryKeys.orders.assignments(), 'my'] as const,
    bulkOperations: () => [...queryKeys.orders.all, 'bulk-operations'] as const,
    documents: () => [...queryKeys.orders.all, 'documents'] as const,
    notes: (orderId: number) =>
      [...queryKeys.orders.detail(orderId), 'notes'] as const,
    statusHistory: (orderId: number) =>
      [...queryKeys.orders.detail(orderId), 'status-history'] as const,
    dashboardStats: () => [...queryKeys.orders.all, 'dashboard-stats'] as const,
  },

  // Products
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (filters: ProductFilters) =>
      [...queryKeys.products.lists(), filters] as const,
    details: () => [...queryKeys.products.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.products.details(), id] as const,
    fullDetails: () => [...queryKeys.products.all, 'full-detail'] as const,
    fullDetail: (id: number) =>
      [...queryKeys.products.fullDetails(), id] as const,
    categories: () => [...queryKeys.products.all, 'categories'] as const,
    categoryTree: () => [...queryKeys.products.categories(), 'tree'] as const,
    category: (id: number) => [...queryKeys.products.categories(), id] as const,
    brands: () => [...queryKeys.products.all, 'brands'] as const,
    brand: (id: number) => [...queryKeys.products.brands(), id] as const,
    productTypes: () => [...queryKeys.products.all, 'product-types'] as const,
    types: () => [...queryKeys.products.all, 'product-types'] as const, // Alias for productTypes
    productType: (id: number) =>
      [...queryKeys.products.productTypes(), id] as const,
    attributes: () => [...queryKeys.products.all, 'attributes'] as const,
    attribute: (id: number) =>
      [...queryKeys.products.attributes(), id] as const,
    attributeValues: () =>
      [...queryKeys.products.all, 'attribute-values'] as const,
    variants: (productId: number) =>
      [...queryKeys.products.detail(productId), 'variants'] as const,
    variant: (productId: number, variantId: number) =>
      [...queryKeys.products.variants(productId), variantId] as const,
    images: (productId: number) =>
      [...queryKeys.products.detail(productId), 'images'] as const,
    analytics: () => [...queryKeys.products.all, 'analytics'] as const,
    bulkOperations: () =>
      [...queryKeys.products.all, 'bulk-operations'] as const,
    brandProductTypes: () =>
      [...queryKeys.products.all, 'brand-product-types'] as const,
    productTypeBrands: (productTypeId: number) =>
      [
        ...queryKeys.products.all,
        'product-type-brands',
        productTypeId,
      ] as const,
    productTypeAttributes: (productTypeId: number) =>
      [
        ...queryKeys.products.all,
        'product-type-attributes',
        productTypeId,
      ] as const,
    attributeValues: (attributeId?: number) =>
      attributeId
        ? ([
            ...queryKeys.products.all,
            'attribute-values',
            attributeId,
          ] as const)
        : ([...queryKeys.products.all, 'attribute-values'] as const),
    attributeValue: (id: number) =>
      [...queryKeys.products.all, 'attribute-values', id] as const,
    images: (variantId?: number) =>
      variantId
        ? ([...queryKeys.products.all, 'images', variantId] as const)
        : ([...queryKeys.products.all, 'images'] as const),
    image: (id: number) => [...queryKeys.products.all, 'images', id] as const,
    sharedImages: (productId: number) =>
      [...queryKeys.products.detail(productId), 'shared-images'] as const,
    sharedImage: (productId: number, imageId: number) =>
      [...queryKeys.products.sharedImages(productId), imageId] as const,
    combinedImages: (variantId: number) =>
      [...queryKeys.products.all, 'combined-images', variantId] as const,
    variantImageLinks: (variantId: number) =>
      [...queryKeys.products.all, 'variant-image-links', variantId] as const,
  },

  // Customers
  customers: {
    all: ['customers'] as const,
    lists: () => [...queryKeys.customers.all, 'list'] as const,
    list: (filters: CustomerFilters) =>
      [...queryKeys.customers.lists(), filters] as const,
    details: () => [...queryKeys.customers.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.customers.details(), id] as const,
    addresses: (customerId: number) =>
      [...queryKeys.customers.detail(customerId), 'addresses'] as const,
    address: (customerId: number, addressId: number) =>
      [...queryKeys.customers.addresses(customerId), addressId] as const,
    orders: (customerId: number) =>
      [...queryKeys.customers.detail(customerId), 'orders'] as const,
    activity: (customerId: number) =>
      [...queryKeys.customers.detail(customerId), 'activity'] as const,
    supportHistory: (customerId: number) =>
      [...queryKeys.customers.detail(customerId), 'support-history'] as const,
    analytics: () => [...queryKeys.customers.all, 'analytics'] as const,
    segments: () => [...queryKeys.customers.all, 'segments'] as const,
  },

  // Staff Management
  staff: {
    all: ['staff'] as const,
    profiles: () => [...queryKeys.staff.all, 'profiles'] as const,
    profile: (id: number) => [...queryKeys.staff.profiles(), id] as const,
    roles: () => [...queryKeys.staff.all, 'roles'] as const,
    role: (id: number) => [...queryKeys.staff.roles(), id] as const,
    permissions: () => [...queryKeys.staff.all, 'permissions'] as const,
    permission: (id: number) => [...queryKeys.staff.permissions(), id] as const,
    groups: () => [...queryKeys.staff.all, 'groups'] as const,
    group: (id: number) => [...queryKeys.staff.groups(), id] as const,
    departmentSummary: () =>
      [...queryKeys.staff.all, 'department-summary'] as const,
    myProfile: () => [...queryKeys.staff.all, 'my-profile'] as const,
  },

  // Analytics & Dashboard
  analytics: {
    all: ['analytics'] as const,
    dashboard: () => [...queryKeys.analytics.all, 'dashboard'] as const,
    sales: () => [...queryKeys.analytics.all, 'sales'] as const,
    salesByPeriod: (period: string) =>
      [...queryKeys.analytics.sales(), period] as const,
    performance: () => [...queryKeys.analytics.all, 'performance'] as const,
    performanceByMetric: (metric: string) =>
      [...queryKeys.analytics.performance(), metric] as const,
    reports: () => [...queryKeys.analytics.all, 'reports'] as const,
    report: (reportId: string) =>
      [...queryKeys.analytics.reports(), reportId] as const,
  },

  // Content Management
  content: {
    all: ['content'] as const,
    reviews: () => [...queryKeys.content.all, 'reviews'] as const,
    review: (id: number) => [...queryKeys.content.reviews(), id] as const,
    moderation: () => [...queryKeys.content.all, 'moderation'] as const,
    moderationQueue: () =>
      [...queryKeys.content.moderation(), 'queue'] as const,
  },

  // System & Settings
  system: {
    all: ['system'] as const,
    settings: () => [...queryKeys.system.all, 'settings'] as const,
    setting: (key: string) => [...queryKeys.system.settings(), key] as const,
    auditLogs: () => [...queryKeys.system.all, 'audit-logs'] as const,
    auditLog: (id: number) => [...queryKeys.system.auditLogs(), id] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
    metrics: () => [...queryKeys.system.all, 'metrics'] as const,
  },

  // Notifications
  notifications: {
    all: ['notifications'] as const,
    list: () => [...queryKeys.notifications.all, 'list'] as const,
    unread: () => [...queryKeys.notifications.all, 'unread'] as const,
    count: () => [...queryKeys.notifications.all, 'count'] as const,
  },
} as const

// Helper functions for common cache operations
export const cacheUtils = {
  /**
   * Invalidate all queries for a specific entity type
   */
  invalidateEntity: (queryClient: any, entity: keyof typeof queryKeys) => {
    return queryClient.invalidateQueries({ queryKey: queryKeys[entity].all })
  },

  /**
   * Invalidate all list queries for an entity
   */
  invalidateEntityLists: (
    queryClient: any,
    entity: 'orders' | 'products' | 'customers'
  ) => {
    return queryClient.invalidateQueries({
      queryKey: queryKeys[entity].lists(),
    })
  },

  /**
   * Remove specific entity from cache
   */
  removeEntity: (
    queryClient: any,
    entity: 'orders' | 'products' | 'customers',
    id: number
  ) => {
    return queryClient.removeQueries({ queryKey: queryKeys[entity].detail(id) })
  },

  /**
   * Set entity data in cache
   */
  setEntityData: <T>(
    queryClient: any,
    entity: 'orders' | 'products' | 'customers',
    id: number,
    data: T
  ) => {
    return queryClient.setQueryData(queryKeys[entity].detail(id), data)
  },

  /**
   * Prefetch entity data
   */
  prefetchEntity: async (
    queryClient: any,
    entity: 'orders' | 'products' | 'customers',
    id: number,
    fetcher: () => Promise<any>
  ) => {
    return queryClient.prefetchQuery({
      queryKey: queryKeys[entity].detail(id),
      queryFn: fetcher,
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  },
}
